import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'
import { remark } from 'remark'
import html from 'remark-html'

import type { ProjectSumary, PostId, PostFull } from './type';
import { z } from 'zod';

export const FrontMatterSchema = z.object({
  title: z.string(),
  tags: z.array(z.string()).optional(),
  author: z.string().optional(),
  short: z.string().optional(),
  image: z.string().optional(),
  
});

export type FrontMatter = z.infer<typeof FrontMatterSchema>;

const projectsDirectory = path.join(process.cwd(), 'src/data/projects')

export function getSortedProjectsData() {
  // Get file names under /posts
  const fileNames = fs.readdirSync(projectsDirectory)
  const allPostsData: ProjectSumary[] = fileNames.map(fileName => {
    // Remove ".md" from file name to get id
    const id = fileName.replace(/\.md$/, '')

    // Read markdown file as string
    const fullPath = path.join(projectsDirectory, fileName)
    const fileContents = fs.readFileSync(fullPath, 'utf8')

    // Use gray-matter to parse the post metadata section
    const matterResult = matter(fileContents)
      // ✅ Validation via Zod
        const data = FrontMatterSchema.parse(matterResult.data);
    // Combine the data with the id
    return {
      id,
      ...data
    }
  })
  // Sort posts by date
  return allPostsData.sort((a, b) => {
    if (a.id > b.id) {
      return 1
    } else {
      return -1
    }
  })
}


/** Retourne la liste des slugs pour `getStaticPaths` (Next .js pages) */
/** Retourne le contenu HTML et la front-matter d’un billet donné */
export async function getProjectData(id: string): Promise<Omit<PostFull, 'date'>> {
  const fullPath = path.join(projectsDirectory, `${id}.md`);
  const fileContents = fs.readFileSync(fullPath, 'utf8');

  
const matterResult = matter(fileContents);
const data = FrontMatterSchema.parse(matterResult.data);
const content = matterResult.content;

  const processed = await remark().use(html).process(content);

  return {
    id,
    contentHtml: processed.toString(),
    ...data,
  };
}
