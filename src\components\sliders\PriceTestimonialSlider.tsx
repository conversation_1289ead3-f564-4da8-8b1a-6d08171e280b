import { sliderProps } from "@/src/common/sliderProps";
import { Swiper, SwiperSlide } from "swiper/react";

const PriceTestimonialSlider = () => {
  return (
    <section className="mil-reviews mil-p-120-120">
      <div className="container">
        <Swiper
          {...sliderProps.milReviSlider2}
          className="swiper-container mil-revi-slider-2"
        >
          <SwiperSlide className="swiper-slide">
            <div className="mil-review mil-text-center">
              <div className="mil-icon-frame mil-icon-frame-md mil-mb-30">
                <img src="img/icons/md/7.svg" alt="icon" />
              </div>
              <p className="mil-mb-30">
                There are many variations of passages of Lorem Ipsum available,
                but the majority have suffered alteration in some form, by
                injected humour, or randomised words slightly believable.
              </p>
              <div className="mil-stars mil-mb-30">
                <ul>
                  <li>
                    <i className="fas fa-star" />
                  </li>
                  <li>
                    <i className="fas fa-star" />
                  </li>
                  <li>
                    <i className="fas fa-star" />
                  </li>
                  <li>
                    <i className="fas fa-star" />
                  </li>
                  <li>
                    <i className="fas fa-star" />
                  </li>
                </ul>
              </div>
              <div className="mil-author">
                <img src="img/faces/1.jpg" alt="Customer" />
                <div className="mil-name">
                  <h6>Margaret Williams</h6>
                  <span className="mil-text-sm">Agency Design</span>
                </div>
              </div>
            </div>
          </SwiperSlide>
          <SwiperSlide className="swiper-slide">
            <div className="mil-review mil-text-center">
              <div className="mil-icon-frame mil-icon-frame-md mil-mb-30">
                <img src="img/icons/md/7.svg" alt="icon" />
              </div>
              <p className="mil-mb-30">
                There are many variations of passages of Lorem Ipsum available,
                but the majority have suffered alteration in some form, by
                injected humour, or randomised words slightly believable.
              </p>
              <div className="mil-stars mil-mb-30">
                <ul>
                  <li>
                    <i className="fas fa-star" />
                  </li>
                  <li>
                    <i className="fas fa-star" />
                  </li>
                  <li>
                    <i className="fas fa-star" />
                  </li>
                  <li>
                    <i className="fas fa-star" />
                  </li>
                  <li>
                    <i className="fas fa-star" />
                  </li>
                </ul>
              </div>
              <div className="mil-author">
                <img src="img/faces/2.jpg" alt="Customer" />
                <div className="mil-name">
                  <h6>Tamzyn French</h6>
                  <span className="mil-text-sm">Agency Design</span>
                </div>
              </div>
            </div>
          </SwiperSlide>
          <SwiperSlide className="swiper-slide">
            <div className="mil-review mil-text-center">
              <div className="mil-icon-frame mil-icon-frame-md mil-mb-30">
                <img src="img/icons/md/7.svg" alt="icon" />
              </div>
              <p className="mil-mb-30">
                There are many variations of passages of Lorem Ipsum available,
                but the majority have suffered alteration in some form, by
                injected humour, or randomised words slightly believable.
              </p>
              <div className="mil-stars mil-mb-30">
                <ul>
                  <li>
                    <i className="fas fa-star" />
                  </li>
                  <li>
                    <i className="fas fa-star" />
                  </li>
                  <li>
                    <i className="fas fa-star" />
                  </li>
                  <li>
                    <i className="fas fa-star" />
                  </li>
                  <li>
                    <i className="fas fa-star" />
                  </li>
                </ul>
              </div>
              <div className="mil-author">
                <img src="img/faces/3.jpg" alt="Customer" />
                <div className="mil-name">
                  <h6>Margaret Williams</h6>
                  <span className="mil-text-sm">Agency Design</span>
                </div>
              </div>
            </div>
          </SwiperSlide>
          <SwiperSlide className="swiper-slide">
            <div className="mil-review mil-text-center">
              <div className="mil-icon-frame mil-icon-frame-md mil-mb-30">
                <img src="img/icons/md/7.svg" alt="icon" />
              </div>
              <p className="mil-mb-30">
                There are many variations of passages of Lorem Ipsum available,
                but the majority have suffered alteration in some form, by
                injected humour, or randomised words slightly believable.
              </p>
              <div className="mil-stars mil-mb-30">
                <ul>
                  <li>
                    <i className="fas fa-star" />
                  </li>
                  <li>
                    <i className="fas fa-star" />
                  </li>
                  <li>
                    <i className="fas fa-star" />
                  </li>
                  <li>
                    <i className="fas fa-star" />
                  </li>
                  <li>
                    <i className="fas fa-star" />
                  </li>
                </ul>
              </div>
              <div className="mil-author">
                <img src="img/faces/4.jpg" alt="Customer" />
                <div className="mil-name">
                  <h6>Tamzyn French</h6>
                  <span className="mil-text-sm">Agency Design</span>
                </div>
              </div>
            </div>
          </SwiperSlide>
          <SwiperSlide className="swiper-slide">
            <div className="mil-review mil-text-center">
              <div className="mil-icon-frame mil-icon-frame-md mil-mb-30">
                <img src="img/icons/md/7.svg" alt="icon" />
              </div>
              <p className="mil-mb-30">
                There are many variations of passages of Lorem Ipsum available,
                but the majority have suffered alteration in some form, by
                injected humour, or randomised words slightly believable.
              </p>
              <div className="mil-stars mil-mb-30">
                <ul>
                  <li>
                    <i className="fas fa-star" />
                  </li>
                  <li>
                    <i className="fas fa-star" />
                  </li>
                  <li>
                    <i className="fas fa-star" />
                  </li>
                  <li>
                    <i className="fas fa-star" />
                  </li>
                  <li>
                    <i className="fas fa-star" />
                  </li>
                </ul>
              </div>
              <div className="mil-author">
                <img src="img/faces/5.jpg" alt="Customer" />
                <div className="mil-name">
                  <h6>Margaret Williams</h6>
                  <span className="mil-text-sm">Agency Design</span>
                </div>
              </div>
            </div>
          </SwiperSlide>
        </Swiper>
      </div>
    </section>
  );
};
export default PriceTestimonialSlider;
